<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_title_recent"
        style="@style/title_fomat"
        android:text="@string/tv_recent_play"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_recent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title_recent" />

    <View
        android:id="@+id/recent_driver"
        android:layout_width="wrap_content"
        android:layout_height="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/recycle_recent" />


    <com.czur.starry.device.wallpaperdisplay.widget.TabBar
        android:id="@+id/selectBar"
        style="@style/title_fomat"
        android:text="@string/tv_meeting_define"
        app:layout_constraintLeft_toLeftOf="@+id/tv_title_recent"
        app:layout_constraintTop_toBottomOf="@+id/recent_driver" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/meet_list"
        android:layout_width="wrap_content"
        android:layout_height="800px"
        android:layout_marginTop="25px"
        app:bl_corners_radius="10px"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="@+id/recycle_recent"
        app:layout_constraintTop_toBottomOf="@+id/selectBar">
    </androidx.recyclerview.widget.RecyclerView>

    <View
        android:layout_width="match_parent"
        android:layout_height="75px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/meet_list" />


</androidx.constraintlayout.widget.ConstraintLayout>