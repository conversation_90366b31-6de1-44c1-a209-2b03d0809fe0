import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.utils.widget.ImageFilterView
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.bean.FileMode
import com.czur.starry.device.wallpaperdisplay.util.WallPaperListItem
import com.czur.starry.device.wallpaperdisplay.util.getDisplayTimeFormat2
import com.czur.starry.device.wallpaperdisplay.util.getSystemProp
import com.czur.starry.device.wallpaperdisplay.util.getTimeSystemProp
import com.czur.starry.device.wallpaperdisplay.util.setSystemProp
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM

private const val TAG = "TemplateAdapter"
private const val ASSETS_PATH = "file:///android_asset/"

class TemplateAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {


    private var countTimer: CountDownTimer? = null
    private var mContext: Context? = null
    private var mData: MutableList<WallPaperListItem>? = null
    private var fileMode = FileMode.RECENT

    private val diffCallback = object : DiffUtil.ItemCallback<FileViewEntity>() {
        override fun areItemsTheSame(oldItem: FileViewEntity, newItem: FileViewEntity): Boolean {
            return (oldItem.fileEntity.wallPaperEntity?.absPath == newItem.fileEntity.wallPaperEntity?.absPath
                    && oldItem.fileEntity.wallPaperEntity?.lastModifyTime == newItem.fileEntity.wallPaperEntity?.lastModifyTime)
        }

        override fun areContentsTheSame(oldItem: FileViewEntity, newItem: FileViewEntity): Boolean {
            return (oldItem == newItem && oldItem.fileEntity.wallPaperEntity?.lastModifyTime == oldItem.fileEntity.wallPaperEntity?.lastModifyTime)
        }
    }
    private val mDiffer: AsyncListDiffer<FileViewEntity> = AsyncListDiffer(this, diffCallback)

    fun getData(position: Int): WallPaperListItem {
        return mDiffer.currentList[position].fileEntity
    }

    fun getAllData(): MutableList<WallPaperListItem>? {
        return mData
    }

    //更新播放状态
    fun modifyData() {
        mDiffer.currentList[0].showMode = false
    }

    fun cancelTimeCount() {
        countTimer?.cancel()
    }

    // 开始倒计时
    private fun startCountDown() {
        countTimer?.cancel()

        val lastTime = getTimeSystemProp().toLong() - System.currentTimeMillis()
        countTimer = object : CountDownTimer(lastTime, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val timeString = getDisplayTimeFormat2(millisUntilFinished / 1000)
                val payload = Bundle()
                payload.putString("onTick", timeString)
                notifyItemChanged(0, payload)
            }

            override fun onFinish() {
                val payload = Bundle()
                payload.putString("onTick", "finish")
                notifyItemChanged(0, payload)
                setSystemProp(false)
            }
        }.start()
    }

    fun setData(
        context: Context,
        data: MutableList<WallPaperListItem>,
        mode: FileMode = FileMode.RECENT
    ) {
        fileMode = mode
        mContext = context
        mData = data

        val showData = data.map {
            FileViewEntity.createByFileEntity(it)
        }
        mDiffer.submitList(showData)

        if (fileMode == FileMode.RECENT) {
            if (getSystemProp()) {
                startCountDown()
            } else {
                cancelTimeCount()
                val payload = Bundle()
                payload.putString("onTick", "finish")
                notifyItemChanged(0, payload)
            }
        }
    }


    fun refreshData(data: MutableList<WallPaperListItem>, fileMode: FileMode = FileMode.RECENT) {
        val showData = data.map {
            FileViewEntity.createByFileEntity(it)
        }

        mDiffer.submitList(showData)

        if (fileMode == FileMode.RECENT) {
            if (getSystemProp()) {
                startCountDown()
            } else {
                cancelTimeCount()
                val payload = Bundle()
                payload.putString("onTick", "finish")
                notifyItemChanged(0, payload)
            }
        }

    }

    fun updateItem(position: Int) {
        notifyItemChanged(position)
    }

    override fun getItemCount(): Int {
        return mDiffer.currentList.size
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemView: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.template_item_layout, parent, false)
        return NormalHolder(itemView)
    }

    override fun onBindViewHolder(
        holder: RecyclerView.ViewHolder,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty() || position != 0) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            val bundle = payloads[0] as Bundle
            val timeString = bundle.getString("onTick", "")
            val normalHolder = holder as NormalHolder
            if (timeString != "finish") {
                normalHolder.mtvTime.show()
                normalHolder.mtvStop.show()
                payloads[0] as Bundle
                normalHolder.mtvTime.text = timeString
            } else {
                normalHolder.mtvTime.gone()
                normalHolder.mtvStop.gone()
            }
        }

    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val normalHolder = holder as NormalHolder
        val data = mDiffer.currentList[position]
        var path = ASSETS_PATH + data.fileEntity.wallPaperEntity?.absPath
        if (data.fileEntity.wallPaperEntity?.absPath!!.contains(DisplayVM.rootDir.absolutePath)) {//自定义
            path = data.fileEntity.wallPaperEntity.absPath
        }
        val lastTag = normalHolder.mImage.tag
//        if (lastTag != path) {
            Glide.with(holder.itemView.context)
                .load(path)
                .signature(ObjectKey(System.currentTimeMillis()))
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        normalHolder.mImage.tag = null
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>?,
                        dataSource: DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        normalHolder.mImage.tag = path
                        normalHolder.progressBar.gone()
                        return false
                    }


                })
                .into(normalHolder.mImage)
//        } else {
//            normalHolder.progressBar.gone()
//        }
        normalHolder.mtvName.text = data.fileEntity.wallPaperEntity.name.substringBeforeLast(".")


        if (fileMode == FileMode.RECENT && getSystemProp() && position == 0) {
            normalHolder.mtvTime.show()
            normalHolder.mtvStop.show()
            normalHolder.mtvTime.text =
                getDisplayTimeFormat2((getTimeSystemProp().toLong() - System.currentTimeMillis()) / 1000)
        } else {
            normalHolder.mtvTime.gone()
            normalHolder.mtvStop.gone()
        }
    }


    class NormalHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var mImage: ImageFilterView
        var mtvName: TextView
        var mtvTime: TextView
        var mtvStop: TextView
        var progressBar: ProgressBar

        init {
            mtvName = itemView.findViewById<TextView>(R.id.tv_name)!!
            mtvTime = itemView.findViewById<TextView>(R.id.tv_play_time)!!
            mtvStop = itemView.findViewById<TextView>(R.id.tv_stop)!!
            mImage = itemView.findViewById<ImageFilterView>(R.id.im_template)!!
            progressBar = itemView.findViewById<ProgressBar>(R.id.progress_bar)!!
        }
    }


}

data class FileViewEntity(
    var showMode: Boolean,            // 是否倒计时
    var refrush: Boolean,
    val fileEntity: WallPaperListItem
) {


    companion object {
        fun createByFileEntity(fileEntity: WallPaperListItem): FileViewEntity {
            return FileViewEntity(
                false,
                false,
                fileEntity
            )
        }
    }

}