package com.czur.starry.device.settings

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import androidx.core.view.isVisible
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.addEmojiFilter
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.settings.databinding.DialogRenameBinding


class ReNameDialogActivity : CZViewBindingAty<DialogRenameBinding>() {
    companion object {
        const val TAG = "launcherRenameDialog"
        private const val ROOM_NAME_SHOW_LOGO = "nullShowLogo"
    }

    private var meetingRoomName: String = ""

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlePreIntent(intent)
        binding.inputDialogEt.setText(meetingRoomName)
        binding.inputDialogEt.setSelection(meetingRoomName.length)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        logTagD("Chen", "onCreate")
    }

    override fun onDestroy() {
        super.onDestroy()
        logTagD("Chen", "onDestroy")
    }

    override fun initWindow() {
        super.initWindow()
        if (Constants.starryHWInfo.hasTouchScreen) {
            logTagV(TAG, "触控屏,添加偏移量")
            window.apply {
                val params = attributes
                params.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        logIntent(preIntent, TAG)
        meetingRoomName = preIntent.getStringExtra("meetingRoomName").orEmpty()
    }

    override fun DialogRenameBinding.initBindingViews() {
        setFinishOnTouchOutside(false)  // 禁止点击空白部分退出
        window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);

        inputDialogEt.apply {
            setText(meetingRoomName)
            setSelection(meetingRoomName.length)
            showTextLength = 30
            addEmojiFilter()

            post {
                requestFocus()
                keyboardShow()
            }

            setOnEditorActionListener { _, actionId, event ->
                if (actionId == EditorInfo.IME_ACTION_DONE  // 输入法软键盘的发送按钮
                    || (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) // 键盘回车键
                ) {
                    if (confirmBtn.isEnabled && confirmBtn.isVisible) {
                        confirmBtn.performClick()
                    }
                }
                true
            }
        }

        confirmBtn.setOnClickListener {
            val text = inputDialogEt.text.toString().trim()
            val saveText = if (text.isNullOrEmpty()) {
                ROOM_NAME_SHOW_LOGO
            } else {
                text
            }
            SettingUtil.PersonalizationSetting.setLauncherMeetingRoomName(saveText)
            finish()
        }

        cancelBtn.setOnClickListener {
            finish()
        }

    }

    override fun finish() {
        super.finish()
        logTagD("Chen", "finish")
        val intent = Intent()
        sendBroadcast(intent)
        overridePendingTransition(0, 0);
    }
}