15:27:44.166 2088  EShareServ...nfigParser com.ecloud.eshare.server             E  checkChengZhe, CBP02C2412000001, XWfHNna9E4i7Ng==
15:27:44.173 2088  EShareServ...workHelper com.ecloud.eshare.server             E  updateNetworkInternal, <ap>, **************, *************, CBP02C2412000001, XWfHNna9E4i7Ng==, 3, [**************, *************]
15:27:44.177 2088  EShareServ...workHelper com.ecloud.eshare.server             W  updateNetwork, sameNetwork, 1|**************|*************|CBP02C2412000001|XWfHNna9E4i7Ng==
15:27:44.183 2088  EShareServ...workHelper com.ecloud.eshare.server             E  updateNetwork, AUTO_REFRESH_NETWORK_INTERVAL.
15:27:44.231 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:44.341 489   alsa_route              android.hardware.audio.service       D  route_info->sound_card 4, route_info->devices 0  
15:27:44.342 17145 Chen                    com.czur.starry.device.launcher      D  startActivity
15:27:44.342 489   alsa_route              android.hardware.audio.service       D  route_set_controls() set route 0
15:27:45.857 1451  <no-tag>                pid-1451                             W  es8156_set_switch_hp(507), es8156->muted = 1
15:27:45.860 1451  <no-tag>                pid-1451                             W  ad82129_startup(241), -----------
15:27:44.347 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.350 770   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
15:27:45.860 1451  <no-tag>                pid-1451                             W  enter into es8156_set_dai_sysclk, freq = 4096000
15:27:45.867 11673 <no-tag>                kworker/u16                          W  enter into es8156_set_bias_level, level = 1
15:27:44.359 770   SplashScre...eptionList system_server                        V  SplashScreen checking exception for package com.czur.starry.device.settings (target sdk:34) -> false
15:27:44.361 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.363 770   ActivityTaskManager     system_server                        I  START u0 {flg=0x18000000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity (has extras)} with LAUNCH_SINGLE_INSTANCE from uid 1000 (BAL_ALLOW_ALLOWLISTED_UID) result code=0
15:27:44.364 981   WindowManagerShell      com.android.systemui                 V  Transition requested: android.os.BinderProxy@5475fe4 TransitionRequestInfo { type = 1, triggerTask = TaskInfo{userId=0 taskId=1050 displayId=0 isRunning=true baseIntent=Intent { flg=0x18800000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity } baseActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} topActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} origActivity=null realActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} numActivities=1 lastActiveTime=91337052 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@cf37c4d} topActivityType=1 pictureInPictureParams=PictureInPictureParams( aspectRatio=16/9 expandedAspectRatio=null sourceRectHint=null hasSetActions=false hasSetCloseAction=false isAutoPipEnabled=false isSeamlessResizeEnabled=true title=null subtitle=null isLaunchIntoPip=false) shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=null topActivityInfo=ActivityInfo{4199302 com.czur.starry.device.settings.ReNameDialogActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isTopActivityTransparent=true locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden}, remoteTransition = null, displayChange = null, flags = 0 }
15:27:44.388 547   BpBinder                surfaceflinger                       I  onLastStrongRef automatically unlinking death recipients: 
15:27:44.400 770   InputDispatcher         system_server                        I  Not sending touch event to 4b1f84 com.czur.starry.device.launcher/com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity because it is paused
15:27:44.401 770   InputDispatcher         system_server                        I  Dropping event because no targets were found: MotionEvent(deviceId=7, eventTime=91337089715000, source=MOUSE, displayId=0, action=DOWN, actionButton=0x00000000, flags=0x00000000, metaState=0x00000000, buttonState=0x00000001, classification=NONE, edgeFlags=0x00000000, xPrecision=1.0, yPrecision=1.0, xCursorPosition=137.1, yCursorPosition=156.2, pointers=[0: (137.1, 156.2)]), policyFlags=0x62000001
15:27:44.401 770   InputDispatcher         system_server                        I  Dropping event because the pointer is not down or we previously dropped the pointer down event in display 0: MotionEvent(deviceId=7, eventTime=91337089715000, source=MOUSE, displayId=0, action=BUTTON_PRESS, actionButton=0x00000001, flags=0x00000000, metaState=0x00000000, buttonState=0x00000001, classification=NONE, edgeFlags=0x00000000, xPrecision=1.0, yPrecision=1.0, xCursorPosition=137.1, yCursorPosition=156.2, pointers=[0: (137.1, 156.2)]), policyFlags=0x62000001
15:27:44.436 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.447 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
15:27:44.472 770   InputDispatcher         system_server                        I  Dropping event because the pointer is not down or we previously dropped the pointer down event in display 0: MotionEvent(deviceId=7, eventTime=91337161698000, source=MOUSE, displayId=0, action=BUTTON_RELEASE, actionButton=0x00000001, flags=0x00000000, metaState=0x00000000, buttonState=0x00000000, classification=NONE, edgeFlags=0x00000000, xPrecision=1.0, yPrecision=1.0, xCursorPosition=137.1, yCursorPosition=156.2, pointers=[0: (137.1, 156.2)]), policyFlags=0x62000000
15:27:44.472 770   InputDispatcher         system_server                        I  Dropping event because the pointer is not down or we previously dropped the pointer down event in display 0: MotionEvent(deviceId=7, eventTime=91337161698000, source=MOUSE, displayId=0, action=UP, actionButton=0x00000000, flags=0x00000000, metaState=0x00000000, buttonState=0x00000000, classification=NONE, edgeFlags=0x00000000, xPrecision=1.0, yPrecision=1.0, xCursorPosition=137.1, yCursorPosition=156.2, pointers=[0: (137.1, 156.2)]), policyFlags=0x62000000
15:27:44.472 770   InputDispatcher         system_server                        I  Not sending touch event to 4b1f84 com.czur.starry.device.launcher/com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity because it is paused
15:27:44.546 17558 AutofillManager         com.czur.starry.device.settings      D  Fill dialog is enabled:false, hints=[]
15:27:44.551 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.launcher
15:27:46.066 11673 <no-tag>                kworker/u16                          W  enter into es8156_set_bias_level, level = 2
15:27:46.067 11673 <no-tag>                kworker/u16                          W  enter into es8156_set_bias_level, level = 3
15:27:44.581 770   ImeTracker              system_server                        I  com.czur.starry.device.launcher:a741ea77: onRequestHide at ORIGIN_SERVER_HIDE_INPUT reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR
15:27:44.581 770   ImeTracker              system_server                        I  com.czur.starry.device.launcher:a741ea77: onCancelled at PHASE_SERVER_SHOULD_HIDE
15:27:44.583 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onFinishInput():2043 
15:27:44.584 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 0, locked = false
15:27:44.585 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.czur.starry.device.launcher, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
15:27:44.585 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:44.587 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 1, locked = false
15:27:44.588 770   PackageConfigPersister  system_server                        W  App-specific configuration not found for packageName: com.czur.starry.device.launcher and userId: 0
15:27:44.590 17558 Chen                    com.czur.starry.device.settings      D  onCreate
15:27:44.631 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.launcher
15:27:44.637 770   WindowManager           system_server                        V  Sent Transition #213 createdAt=06-07 15:27:44.349 via request=TransitionRequestInfo { type = 1, triggerTask = TaskInfo{userId=0 taskId=1050 displayId=0 isRunning=true baseIntent=Intent { flg=0x18800000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity } baseActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} topActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} origActivity=null realActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} numActivities=1 lastActiveTime=91337052 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{4f72fbf Task{fd4a448 #1050 type=standard A=1000:com.czur.starry.device.settings}}} topActivityType=1 pictureInPictureParams=PictureInPictureParams( aspectRatio=16/9 expandedAspectRatio=null sourceRectHint=null hasSetActions=false hasSetCloseAction=false isAutoPipEnabled=false isSeamlessResizeEnabled=true title=null subtitle=null isLaunchIntoPip=false) shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=null topActivityInfo=ActivityInfo{a85b68c com.czur.starry.device.settings.ReNameDialogActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isTopActivityTransparent=true locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden}, remoteTransition = null, displayChange = null, flags = 0 }
15:27:44.638 770   WindowManager           system_server                        V      startWCT=WindowContainerTransaction { changes = {} hops = [] errorCallbackToken=null taskFragmentOrganizer=null }
15:27:44.638 770   WindowManager           system_server                        V      info={id=213 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{RemoteToken{32854d7 Task{26ab1c3 #1037 type=home}}} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0x176a671 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:44.638 17145 StatusBarFragment       com.czur.starry.device.launcher      D  刷新外设模式
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V  onTransitionReady android.os.BinderProxy@5475fe4: {id=213 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@d089049} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0xf3a6413 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V  Playing animation for (#213)android.os.BinderProxy@5475fe4@0
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.DefaultMixedHandler@6252dab
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.keyguard.KeyguardTransitionHandler@f00bc08
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.pip.PipTransition@2caafa1
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.activityembedding.ActivityEmbeddingController@95605c6
15:27:44.638 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.recents.RecentsTransitionHandler@c86f987
15:27:44.638 981   ShellRecents            com.android.systemui                 V  RecentsTransitionHandler.startAnimation: no controller found
15:27:44.639 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.splitscreen.StageCoordinator@ae990b4
15:27:44.639 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.RemoteTransitionHandler@ec1a4dd
15:27:44.639 981   WindowManagerShell      com.android.systemui                 V  Transition doesn't have explicit remote, search filters for match for {id=213 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@d089049} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0xf3a6413 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:44.639 981   WindowManagerShell      com.android.systemui                 V   Delegate animation for #213 to null
15:27:44.639 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.DefaultTransitionHandler@89cb852
15:27:44.639 981   WindowManagerShell      com.android.systemui                 V  start default transition animation, info = {id=213 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@d089049} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0xf3a6413 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:44.640 981   WindowManagerShell      com.android.systemui                 V  Transition animation finished (aborted=false), notifying core (#213)android.os.BinderProxy@5475fe4@0
15:27:44.642 770   WindowManager           system_server                        V  Finish Transition #213: created at 06-07 15:27:44.349 collect-started=0.072ms request-sent=13.391ms started=15.47ms ready=283.388ms sent=286.885ms finished=292.005ms
15:27:44.643 17145 ShareViewModel          com.czur.starry.device.launcher      V  refreshGadgetState
15:27:44.643 981   WindowManagerShell      com.android.systemui                 V  Track 0 became idle
15:27:44.643 981   WindowManagerShell      com.android.systemui                 V  All active transition animations finished
15:27:44.643 981   WindowManagerShell      com.android.systemui                 V   animated by com.android.wm.shell.transition.DefaultTransitionHandler@89cb852
15:27:44.643 981   WindowManagerShell      com.android.systemui                 V  Track 0 became idle
15:27:44.643 981   WindowManagerShell      com.android.systemui                 V  All active transition animations finished
15:27:44.645 17145 SystemManagerProxy      com.czur.starry.device.launcher      V  getGadgetMode Start
15:27:44.646 17145 SystemManager[Java]     com.czur.starry.device.launcher      I  controlUsbMode: -1
15:27:44.646 17145 SystemManagerService    com.czur.starry.device.launcher      D  controlUSBMode Bp Enter
15:27:44.647 660   SystemManagerService    systemmanagerserver                  D  onTransact Bn Enter, code:13
15:27:44.647 660   SystemManagerService    systemmanagerserver                  D  controlUSBMode NEnter
15:27:44.647 660   SystemManagerService    systemmanagerserver                  D  SystemManagerService::controlUSBMode(-1) result: 0
15:27:44.647 17145 SystemManagerService    com.czur.starry.device.launcher      D  controlUSBMode Bp Leave: ret:0
15:27:44.648 17145 SystemManagerProxy      com.czur.starry.device.launcher      D  isGadgetMode Value: 0
15:27:44.648 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/background/cgroup.procs: No such file or directory
15:27:44.648 17145 ShareViewModel          com.czur.starry.device.launcher      V  USB外设模式 = USB_GADGET_MODE_OFF
15:27:44.648 770   ProcessStats            system_server                        W  Tracking association SourceState{4ae0b28 com.czur.starry.device.launcher/1000 BTop #9965} whose proc state 2 is better than process ProcessState{87a92e9 com.czur.starry.device.noticecenter/1000 pkg=com.czur.starry.device.noticecenter} proc state 4 (3 skipped)
15:27:44.649 17145 ShareViewModel          com.czur.starry.device.launcher      V  peripheralModeRunningFlow byom = false, usb = false
15:27:44.650 17145 StatusBarFragment       com.czur.starry.device.launcher      V  byomAndUsbRunning:PeripheralMode(byom=false, usb=false)
15:27:44.656 17145 LauncherMainFragment    com.czur.starry.device.launcher      V  显示引导UI
15:27:44.657 770   CoreBackPreview         system_server                        D  Window{cc9b460 u0 com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@4d591d5, mPriority=0, mIsAnimationCallback=false}
15:27:44.660 17145 AppInfoViewModel        com.czur.starry.device.launcher      D  加载收藏应用列表
15:27:44.669 17145 LauncherActivity        com.czur.starry.device.launcher      D  Launcher doRepeatResume
15:27:44.669 17145 LauncherActivity        com.czur.starry.device.launcher      D  发送LauncherResume广播
15:27:44.670 17145 ContextImpl             com.czur.starry.device.launcher      W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 android.content.ContextWrapper.sendBroadcast:504 com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity.sendLauncherResumeBroadcast:514 com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity.doRepeatResume:390 
15:27:44.671 770   ActivityManager         system_server                        E  Sending non-protected broadcast czur.intent.action.APP_RESUME from system 17145:com.czur.starry.device.launcher/1000 pkg com.czur.starry.device.launcher (Ask Gemini)
                                                                                   java.lang.Throwable
                                                                                   	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:14393)
                                                                                   	at com.android.server.am.ActivityManagerService.broadcastIntentLockedTraced(ActivityManagerService.java:15218)
                                                                                   	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14457)
                                                                                   	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:15423)
                                                                                   	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2668)
                                                                                   	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2766)
                                                                                   	at android.os.Binder.execTransactInternal(Binder.java:1358)
                                                                                   	at android.os.Binder.execTransact(Binder.java:1304)
15:27:44.679 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:44.695 17145 EShareUtil              com.czur.starry.device.launcher      D  showDeviceNameAlertWindow:没有启用设备名称弹窗
15:27:44.696 17145 LauncherActivity        com.czur.starry.device.launcher      D  ==enableEShareVoice===
15:27:44.697 17145 EShareUtil              com.czur.starry.device.launcher      D  enableEShareVoice:true
15:27:44.702 770   UserManagerService      system_server                        I  getUserInfo callingPackage: Package{225225d com.ecloud.eairplay}
15:27:44.703 770   ActivityTaskManager     system_server                        W  Request to remove task ignored for non-existent task 1049
15:27:44.704 17145 LauncherActivity        com.czur.starry.device.launcher      V  防止自动对焦服务挂掉
15:27:44.705 17145 ContextImpl             com.czur.starry.device.launcher      W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 android.content.ContextWrapper.startService:825 com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity$bootCZKeyStone$2.invokeSuspend:415 kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith:33 
15:27:44.709 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - 不支持,直接退出
15:27:44.709 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/background/cgroup.procs: No such file or directory
15:27:44.712 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - onStartCommand
15:27:44.712 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - 不支持,直接退出
15:27:44.713 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - onDestroy
15:27:44.713 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - Stop Listen Sensor Data Failure !
15:27:44.714 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/background/cgroup.procs: No such file or directory
15:27:44.727 17558 OpenGLRenderer          com.czur.starry.device.settings      E  Unable to match the desired swap behavior.
15:27:44.728 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:44.728 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:44.729 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#12(BLAST Consumer)12'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007f4
15:27:44.729 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:44.729 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:44.729 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:44.729 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:44.730 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#12(BLAST Consumer)12'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007f5
15:27:44.730 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:44.730 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:44.730 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:44.730 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:44.731 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#12(BLAST Consumer)12'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007f6
15:27:44.731 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:44.731 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:44.744 17145 OtherQuick...AppManager com.czur.starry.device.launcher      D  从缓存中获取网络会议App
15:27:44.756 17145 OtherMeeti...AppManager com.czur.starry.device.launcher      V  下一次自动刷新时间:3600s 后
15:27:44.768 17558 AutofillManager         com.czur.starry.device.settings      D  notifyViewReadyInner:1073741824
15:27:44.781 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:44.781 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=456, alloc_height=596
15:27:44.781 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#12(BLAST Consumer)12'. share_fd : 9, share_attr_fd : 10, width : 456, height : 596, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1106176, layer_count : 1, backing_store_id: 0x1ef000007f7
15:27:44.781 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 1856, alloc_width : 456, alloc_height : 596
15:27:44.781 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:44.788 770   ActivityTaskManager     system_server                        E   intent.getAction()null
15:27:44.788 770   ActivityTaskManager     system_server                        E   shortComponentName com.czur.starry.device.settings/.ReNameDialogActivity
15:27:44.814 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:44.819 17558 CZURAtyManager          com.czur.starry.device.settings      D  com.czur.starry.device.settings已经没有可见页面
15:27:44.821 17558 Chen                    com.czur.starry.device.settings      D  onDestroy
15:27:44.823 17558 WindowOnBackDispatcher  com.czur.starry.device.settings      W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda17@d50e810
15:27:44.824 770   CoreBackPreview         system_server                        D  Window{cc9b460 u0 com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity}: Setting back callback null
15:27:44.830 17145 Chen                    com.czur.starry.device.launcher      D  startActivity
15:27:44.831 770   InputManager-JNI        system_server                        W  Input channel object 'cc9b460 com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity (client)' was disposed without first being removed with the input manager!
15:27:44.833 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.836 770   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
15:27:44.841 770   SplashScre...eptionList system_server                        V  SplashScreen checking exception for package com.czur.starry.device.settings (target sdk:34) -> false
15:27:44.842 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.843 770   ActivityTaskManager     system_server                        I  START u0 {flg=0x18000000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity (has extras)} with LAUNCH_SINGLE_INSTANCE from uid 1000 (BAL_ALLOW_ALLOWLISTED_UID) result code=0
15:27:44.844 981   WindowManagerShell      com.android.systemui                 V  Transition requested: android.os.BinderProxy@bacc77c TransitionRequestInfo { type = 1, triggerTask = TaskInfo{userId=0 taskId=1051 displayId=0 isRunning=true baseIntent=Intent { flg=0x18800000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity } baseActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} topActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} origActivity=null realActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} numActivities=1 lastActiveTime=91337532 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@340e805} topActivityType=1 pictureInPictureParams=PictureInPictureParams( aspectRatio=16/9 expandedAspectRatio=null sourceRectHint=null hasSetActions=false hasSetCloseAction=false isAutoPipEnabled=false isSeamlessResizeEnabled=true title=null subtitle=null isLaunchIntoPip=false) shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=null topActivityInfo=ActivityInfo{232805a com.czur.starry.device.settings.ReNameDialogActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isTopActivityTransparent=true locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden}, remoteTransition = null, displayChange = null, flags = 0 }
15:27:44.848 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.875 547   BpBinder                surfaceflinger                       I  onLastStrongRef automatically unlinking death recipients: 
15:27:44.897 17558 ImeTracker              com.czur.starry.device.settings      I  com.czur.starry.device.settings:rename:54b3cc65: onRequestShow at ORIGIN_CLIENT_SHOW_SOFT_INPUT reason SHOW_SOFT_INPUT
15:27:44.898 17558 ImeTracker              com.czur.starry.device.settings      I  com.czur.starry.device.settings:rename:54b3cc65: onFailed at PHASE_CLIENT_VIEW_SERVED
15:27:44.899 17558 InputMethodManager      com.czur.starry.device.settings      W  Ignoring showSoftInput() as view=androidx.appcompat.widget.AppCompatEditText{9b419c VFED..CL. .F....ID 30,149-470,209 #7f090232 app:id/inputDialogEt aid=1073741824} is not served.
15:27:44.902 770   InputDispatcher         system_server                        I  Not sending touch event to 4b1f84 com.czur.starry.device.launcher/com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity because it is paused
15:27:44.902 770   InputDispatcher         system_server                        I  Dropping event because no targets were found: MotionEvent(deviceId=7, eventTime=91337591672000, source=MOUSE, displayId=0, action=HOVER_MOVE, actionButton=0x00000000, flags=0x00000000, metaState=0x00000000, buttonState=0x00000000, classification=NONE, edgeFlags=0x00000000, xPrecision=1.0, yPrecision=1.0, xCursorPosition=137.1, yCursorPosition=156.2, pointers=[0: (137.1, 156.2)]), policyFlags=0x62000000
15:27:44.924 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:44.928 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
15:27:44.968 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.launcher
15:27:45.005 506   WifiHAL                 android.hardware.wifi-service        I  Creating message to get link statistics; iface = 10
15:27:45.011 506   WifiHAL                 android.hardware.wifi-service        I  In GetLinkStatsCommand::handleResponse
15:27:45.012 17558 AutofillManager         com.czur.starry.device.settings      D  Fill dialog is enabled:false, hints=[]
15:27:45.015 770   ImeTracker              system_server                        I  com.czur.starry.device.launcher:4af7ff14: onRequestHide at ORIGIN_SERVER_HIDE_INPUT reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR
15:27:45.015 770   ImeTracker              system_server                        I  com.czur.starry.device.launcher:4af7ff14: onCancelled at PHASE_SERVER_SHOULD_HIDE
15:27:45.017 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onFinishInput():2043 
15:27:45.017 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 0, locked = false
15:27:45.018 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.czur.starry.device.launcher, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
15:27:45.018 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.019 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 1, locked = false
15:27:45.020 770   PackageConfigPersister  system_server                        W  App-specific configuration not found for packageName: com.czur.starry.device.launcher and userId: 0
15:27:45.055 17558 Chen                    com.czur.starry.device.settings      D  onCreate
15:27:45.096 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.launcher
15:27:45.098 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
15:27:45.100 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:45.102 770   WindowManager           system_server                        V  Sent Transition #214 createdAt=06-07 15:27:44.835 via request=TransitionRequestInfo { type = 1, triggerTask = TaskInfo{userId=0 taskId=1051 displayId=0 isRunning=true baseIntent=Intent { flg=0x18800000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity } baseActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} topActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} origActivity=null realActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} numActivities=1 lastActiveTime=91337532 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{be3ccfd Task{6c09042 #1051 type=standard A=1000:com.czur.starry.device.settings}}} topActivityType=1 pictureInPictureParams=PictureInPictureParams( aspectRatio=16/9 expandedAspectRatio=null sourceRectHint=null hasSetActions=false hasSetCloseAction=false isAutoPipEnabled=false isSeamlessResizeEnabled=true title=null subtitle=null isLaunchIntoPip=false) shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=null topActivityInfo=ActivityInfo{1218df2 com.czur.starry.device.settings.ReNameDialogActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isTopActivityTransparent=true locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden}, remoteTransition = null, displayChange = null, flags = 0 }
15:27:45.103 770   WindowManager           system_server                        V      startWCT=WindowContainerTransaction { changes = {} hops = [] errorCallbackToken=null taskFragmentOrganizer=null }
15:27:45.103 770   WindowManager           system_server                        V      info={id=214 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{RemoteToken{32854d7 Task{26ab1c3 #1037 type=home}}} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0x176a671 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.103 981   WindowManagerShell      com.android.systemui                 V  onTransitionReady android.os.BinderProxy@bacc77c: {id=214 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@6ecff81} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0x238428b sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.103 981   WindowManagerShell      com.android.systemui                 V  Playing animation for (#214)android.os.BinderProxy@bacc77c@0
15:27:45.103 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.DefaultMixedHandler@6252dab
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.keyguard.KeyguardTransitionHandler@f00bc08
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.pip.PipTransition@2caafa1
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.activityembedding.ActivityEmbeddingController@95605c6
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.recents.RecentsTransitionHandler@c86f987
15:27:45.104 981   ShellRecents            com.android.systemui                 V  RecentsTransitionHandler.startAnimation: no controller found
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.splitscreen.StageCoordinator@ae990b4
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.RemoteTransitionHandler@ec1a4dd
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V  Transition doesn't have explicit remote, search filters for match for {id=214 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@6ecff81} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0x238428b sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   Delegate animation for #214 to null
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.DefaultTransitionHandler@89cb852
15:27:45.104 981   WindowManagerShell      com.android.systemui                 V  start default transition animation, info = {id=214 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@6ecff81} m=CHANGE f=SHOW_WALLPAPER|MOVE_TO_TOP leash=Surface(name=Task=1037)/@0x238428b sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.105 981   WindowManagerShell      com.android.systemui                 V  Transition animation finished (aborted=false), notifying core (#214)android.os.BinderProxy@bacc77c@0
15:27:45.107 770   WindowManager           system_server                        V  Finish Transition #214: created at 06-07 15:27:44.835 collect-started=0.05ms request-sent=7.882ms started=17.189ms ready=263.322ms sent=266.334ms finished=272.008ms
15:27:45.109 981   WindowManagerShell      com.android.systemui                 V  Track 0 became idle
15:27:45.109 981   WindowManagerShell      com.android.systemui                 V  All active transition animations finished
15:27:45.109 981   WindowManagerShell      com.android.systemui                 V   animated by com.android.wm.shell.transition.DefaultTransitionHandler@89cb852
15:27:45.109 981   WindowManagerShell      com.android.systemui                 V  Track 0 became idle
15:27:45.109 981   WindowManagerShell      com.android.systemui                 V  All active transition animations finished
15:27:45.115 770   CoreBackPreview         system_server                        D  Window{cbac166 u0 com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@fb55c43, mPriority=0, mIsAnimationCallback=false}
15:27:45.118 17145 StatusBarFragment       com.czur.starry.device.launcher      D  刷新外设模式
15:27:45.120 17145 ShareViewModel          com.czur.starry.device.launcher      V  refreshGadgetState
15:27:45.121 17145 SystemManagerProxy      com.czur.starry.device.launcher      V  getGadgetMode Start
15:27:45.121 17145 SystemManager[Java]     com.czur.starry.device.launcher      I  controlUsbMode: -1
15:27:45.122 17145 SystemManagerService    com.czur.starry.device.launcher      D  controlUSBMode Bp Enter
15:27:45.122 660   SystemManagerService    systemmanagerserver                  D  onTransact Bn Enter, code:13
15:27:45.122 660   SystemManagerService    systemmanagerserver                  D  controlUSBMode NEnter
15:27:45.122 660   SystemManagerService    systemmanagerserver                  D  SystemManagerService::controlUSBMode(-1) result: 0
15:27:45.122 17145 SystemManagerService    com.czur.starry.device.launcher      D  controlUSBMode Bp Leave: ret:0
15:27:45.123 17145 SystemManagerProxy      com.czur.starry.device.launcher      D  isGadgetMode Value: 0
15:27:45.123 17145 ShareViewModel          com.czur.starry.device.launcher      V  USB外设模式 = USB_GADGET_MODE_OFF
15:27:45.124 17145 ShareViewModel          com.czur.starry.device.launcher      V  peripheralModeRunningFlow byom = false, usb = false
15:27:45.125 17145 StatusBarFragment       com.czur.starry.device.launcher      V  byomAndUsbRunning:PeripheralMode(byom=false, usb=false)
15:27:45.132 17145 LauncherMainFragment    com.czur.starry.device.launcher      V  显示引导UI
15:27:45.136 17145 AppInfoViewModel        com.czur.starry.device.launcher      D  加载收藏应用列表
15:27:45.146 17145 LauncherActivity        com.czur.starry.device.launcher      D  Launcher doRepeatResume
15:27:45.146 17145 LauncherActivity        com.czur.starry.device.launcher      D  发送LauncherResume广播
15:27:45.147 17145 ContextImpl             com.czur.starry.device.launcher      W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 android.content.ContextWrapper.sendBroadcast:504 com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity.sendLauncherResumeBroadcast:514 com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity.doRepeatResume:390 
15:27:45.148 770   ActivityManager         system_server                        E  Sending non-protected broadcast czur.intent.action.APP_RESUME from system 17145:com.czur.starry.device.launcher/1000 pkg com.czur.starry.device.launcher (Ask Gemini)
                                                                                   java.lang.Throwable
                                                                                   	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:14393)
                                                                                   	at com.android.server.am.ActivityManagerService.broadcastIntentLockedTraced(ActivityManagerService.java:15218)
                                                                                   	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14457)
                                                                                   	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:15423)
                                                                                   	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2668)
                                                                                   	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2766)
                                                                                   	at android.os.Binder.execTransactInternal(Binder.java:1358)
                                                                                   	at android.os.Binder.execTransact(Binder.java:1304)
15:27:45.156 17558 OpenGLRenderer          com.czur.starry.device.settings      E  Unable to match the desired swap behavior.
15:27:45.157 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.157 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:45.158 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#13(BLAST Consumer)13'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007f8
15:27:45.158 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:45.158 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.159 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.159 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:45.160 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#13(BLAST Consumer)13'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007f9
15:27:45.160 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:45.160 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.161 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.161 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:45.161 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#13(BLAST Consumer)13'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007fa
15:27:45.161 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:45.161 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.168 17145 EShareUtil              com.czur.starry.device.launcher      D  showDeviceNameAlertWindow:没有启用设备名称弹窗
15:27:45.168 17145 LauncherActivity        com.czur.starry.device.launcher      D  ==enableEShareVoice===
15:27:45.173 17145 EShareUtil              com.czur.starry.device.launcher      D  enableEShareVoice:true
15:27:45.183 17145 LauncherActivity        com.czur.starry.device.launcher      V  防止自动对焦服务挂掉
15:27:45.183 17558 AutofillManager         com.czur.starry.device.settings      D  notifyViewReadyInner:1073741824
15:27:45.186 17145 ContextImpl             com.czur.starry.device.launcher      W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 android.content.ContextWrapper.startService:825 com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity$bootCZKeyStone$2.invokeSuspend:415 kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith:33 
15:27:45.191 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - 不支持,直接退出
15:27:45.193 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - onStartCommand
15:27:45.193 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - 不支持,直接退出
15:27:45.194 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - onDestroy
15:27:45.194 2495  CZUR_keystone           com.czur.keystone                    D  KeystoneService - Stop Listen Sensor Data Failure !
15:27:45.195 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/background/cgroup.procs: No such file or directory
15:27:45.196 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.196 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=456, alloc_height=596
15:27:45.197 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#13(BLAST Consumer)13'. share_fd : 9, share_attr_fd : 10, width : 456, height : 596, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1106176, layer_count : 1, backing_store_id: 0x1ef000007fb
15:27:45.197 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 1856, alloc_width : 456, alloc_height : 596
15:27:45.197 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.202 770   ActivityTaskManager     system_server                        E   intent.getAction()null
15:27:45.202 770   ActivityTaskManager     system_server                        E   shortComponentName com.czur.starry.device.settings/.ReNameDialogActivity
15:27:45.204 17145 OtherQuick...AppManager com.czur.starry.device.launcher      D  从缓存中获取网络会议App
15:27:45.227 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/background/cgroup.procs: No such file or directory
15:27:45.229 17145 Chen                    com.czur.starry.device.launcher      D  startActivity
15:27:45.232 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:45.235 770   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
15:27:45.239 770   SplashScre...eptionList system_server                        V  SplashScreen checking exception for package com.czur.starry.device.settings (target sdk:34) -> false
15:27:45.239 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:45.241 770   ActivityTaskManager     system_server                        I  START u0 {flg=0x18000000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity (has extras)} with LAUNCH_SINGLE_INSTANCE from uid 1000 (BAL_ALLOW_ALLOWLISTED_UID) result code=2
15:27:45.241 981   WindowManagerShell      com.android.systemui                 V  Transition requested: android.os.BinderProxy@a348a14 TransitionRequestInfo { type = 1, triggerTask = TaskInfo{userId=0 taskId=1051 displayId=0 isRunning=true baseIntent=Intent { flg=0x18800000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity } baseActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} topActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} origActivity=null realActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} numActivities=1 lastActiveTime=91337658 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@9e912bd} topActivityType=1 pictureInPictureParams=PictureInPictureParams( aspectRatio=16/9 expandedAspectRatio=null sourceRectHint=null hasSetActions=false hasSetCloseAction=false isAutoPipEnabled=false isSeamlessResizeEnabled=true title=null subtitle=null isLaunchIntoPip=false) shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=null topActivityInfo=ActivityInfo{720c0b2 com.czur.starry.device.settings.ReNameDialogActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isTopActivityTransparent=true locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden}, remoteTransition = null, displayChange = null, flags = 0 }
15:27:45.246 981   WindowManagerShell      com.android.systemui                 D  onActivityRestartAttempt: ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity}
15:27:45.250 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:45.261 17145 OtherMeeti...AppManager com.czur.starry.device.launcher      V  下一次自动刷新时间:3600s 后
15:27:45.274 547   BpBinder                surfaceflinger                       I  onLastStrongRef automatically unlinking death recipients: 
15:27:45.306 17558 ImeTracker              com.czur.starry.device.settings      I  com.czur.starry.device.settings:rename:d1a971ca: onRequestShow at ORIGIN_CLIENT_SHOW_SOFT_INPUT reason SHOW_SOFT_INPUT
15:27:45.308 17558 ImeTracker              com.czur.starry.device.settings      I  com.czur.starry.device.settings:rename:d1a971ca: onFailed at PHASE_CLIENT_VIEW_SERVED
15:27:45.309 17558 InputMethodManager      com.czur.starry.device.settings      W  Ignoring showSoftInput() as view=androidx.appcompat.widget.AppCompatEditText{1faa28c VFED..CL. .F....ID 30,149-470,209 #7f090232 app:id/inputDialogEt aid=1073741824} is not served.
15:27:45.330 770   InputDispatcher         system_server                        I  Not sending touch event to 4b1f84 com.czur.starry.device.launcher/com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity because it is paused
15:27:45.330 770   InputDispatcher         system_server                        I  Dropping event because no targets were found: MotionEvent(deviceId=7, eventTime=91338019682000, source=MOUSE, displayId=0, action=HOVER_MOVE, actionButton=0x00000000, flags=0x00000000, metaState=0x00000000, buttonState=0x00000000, classification=NONE, edgeFlags=0x00000000, xPrecision=1.0, yPrecision=1.0, xCursorPosition=137.1, yCursorPosition=156.2, pointers=[0: (137.1, 156.2)]), policyFlags=0x62000000
15:27:45.346 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:45.346 770   Transition              system_server                        E  Trying to add a ready-group twice: Display{#0 state=ON size=1920x1200 ROTATION_0}
15:27:45.348 770   libprocessgroup         system_server                        W  SetCgroup::ExecuteForProcess: failed to open /dev/stune/top-app/cgroup.procs: No such file or directory
15:27:45.384 17558 OpenGLRenderer          com.czur.starry.device.settings      E  Unable to match the desired swap behavior.
15:27:45.384 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.384 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:45.385 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#14(BLAST Consumer)14'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007fc
15:27:45.385 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:45.385 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.385 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.385 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:45.385 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#14(BLAST Consumer)14'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007fd
15:27:45.386 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:45.386 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.386 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.386 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=596, alloc_height=456
15:27:45.386 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#14(BLAST Consumer)14'. share_fd : 9, share_attr_fd : 10, width : 596, height : 456, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1108992, layer_count : 1, backing_store_id: 0x1ef000007fe
15:27:45.386 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 2432, alloc_width : 596, alloc_height : 456
15:27:45.387 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.393 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.393 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=456, alloc_height=596
15:27:45.394 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#14(BLAST Consumer)14'. share_fd : 9, share_attr_fd : 10, width : 456, height : 596, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1106176, layer_count : 1, backing_store_id: 0x1ef000007ff
15:27:45.394 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 1856, alloc_width : 456, alloc_height : 596
15:27:45.394 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.402 770   ActivityTaskManager     system_server                        E   intent.getAction()null
15:27:45.402 770   ActivityTaskManager     system_server                        E   shortComponentName com.czur.starry.device.settings/.ReNameDialogActivity
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V  onTransitionReady android.os.BinderProxy@a348a14: {id=215 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@5991db9} m=TO_FRONT f=TRANSLUCENT|MOVE_TO_TOP leash=Surface(name=Task=1051)/@0xf547803 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.409 770   ziparchive              system_server                        W  Unable to open '/data/app/~~dwy_UB3aA9ywclt1v1P2Mg==/com.czur.starry.device.settings-YKr9DAVyL-FxEBov_PrTwA==/base.dm': No such file or directory
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V  Playing animation for (#215)android.os.BinderProxy@a348a14@0
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.DefaultMixedHandler@6252dab
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.keyguard.KeyguardTransitionHandler@f00bc08
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.pip.PipTransition@2caafa1
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.activityembedding.ActivityEmbeddingController@95605c6
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.recents.RecentsTransitionHandler@c86f987
15:27:45.409 981   ShellRecents            com.android.systemui                 V  RecentsTransitionHandler.startAnimation: no controller found
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.splitscreen.StageCoordinator@ae990b4
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.RemoteTransitionHandler@ec1a4dd
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V  Transition doesn't have explicit remote, search filters for match for {id=215 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@5991db9} m=TO_FRONT f=TRANSLUCENT|MOVE_TO_TOP leash=Surface(name=Task=1051)/@0xf547803 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.409 981   WindowManagerShell      com.android.systemui                 V   Delegate animation for #215 to null
15:27:45.410 981   WindowManagerShell      com.android.systemui                 V   try handler com.android.wm.shell.transition.DefaultTransitionHandler@89cb852
15:27:45.410 981   WindowManagerShell      com.android.systemui                 V  start default transition animation, info = {id=215 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{android.window.IWindowContainerToken$Stub$Proxy@5991db9} m=TO_FRONT f=TRANSLUCENT|MOVE_TO_TOP leash=Surface(name=Task=1051)/@0xf547803 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.410 981   WindowManagerShell      com.android.systemui                 V  loadAnimation: anim=android.view.animation.AnimationSet@f5635fe animAttr=0x4 type=OPEN isEntrance=true
15:27:45.415 981   WindowManagerShell      com.android.systemui                 V   animated by com.android.wm.shell.transition.DefaultTransitionHandler@89cb852
15:27:45.418 770   WindowManager           system_server                        V  getPackagePerformanceMode -- ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} -- com.czur.starry.device.settings -- mode=0
15:27:45.419 770   WindowManager           system_server                        V  Sent Transition #215 createdAt=06-07 15:27:45.234 via request=TransitionRequestInfo { type = 1, triggerTask = TaskInfo{userId=0 taskId=1051 displayId=0 isRunning=true baseIntent=Intent { flg=0x18800000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity } baseActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} topActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} origActivity=null realActivity=ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} numActivities=1 lastActiveTime=91337658 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{be3ccfd Task{6c09042 #1051 type=standard A=1000:com.czur.starry.device.settings}}} topActivityType=1 pictureInPictureParams=PictureInPictureParams( aspectRatio=16/9 expandedAspectRatio=null sourceRectHint=null hasSetActions=false hasSetCloseAction=false isAutoPipEnabled=false isSeamlessResizeEnabled=true title=null subtitle=null isLaunchIntoPip=false) shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=null topActivityInfo=ActivityInfo{1218df2 com.czur.starry.device.settings.ReNameDialogActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 isUserFullscreenOverrideEnabled=false isTopActivityTransparent=true locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden}, remoteTransition = null, displayChange = null, flags = 0 }
15:27:45.419 770   WindowManager           system_server                        V      startWCT=WindowContainerTransaction { changes = {} hops = [] errorCallbackToken=null taskFragmentOrganizer=null }
15:27:45.419 770   WindowManager           system_server                        V      info={id=215 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{WCT{RemoteToken{be3ccfd Task{6c09042 #1051 type=standard A=1000:com.czur.starry.device.settings}}} m=TO_FRONT f=TRANSLUCENT|MOVE_TO_TOP leash=Surface(name=Task=1051)/@0x2ec5016 sb=Rect(0, 0 - 1920, 1200) eb=Rect(0, 0 - 1920, 1200) d=0}]}
15:27:45.425 770   UserManagerService      system_server                        I  getUserInfo callingPackage: Package{225225d com.ecloud.eairplay}
15:27:45.434 770   ImeTracker              system_server                        I  com.czur.starry.device.settings:55ada45a: onRequestHide at ORIGIN_SERVER_HIDE_INPUT reason HIDE_STATE_HIDDEN_FORWARD_NAV
15:27:45.435 770   ImeTracker              system_server                        I  com.czur.starry.device.settings:55ada45a: onCancelled at PHASE_SERVER_SHOULD_HIDE
15:27:45.442 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onFinishInput():2043 
15:27:45.445 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 0, locked = false
15:27:45.447 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.czur.starry.device.settings, inputType=1, inputTypeString=Normal[], enableLearning=true, autoCorrection=true, autoComplete=true, imeOptions=8000006, privateImeOptions=null, actionName=DONE, actionLabel=null, initialSelStart=0, initialSelEnd=0, initialCapsMode=0, label=null, fieldId=**********, fieldName=null, extras=Bundle[mParcelledData.dataSize=240], hintText=non-empty, hintLocales=[]}}, false)
15:27:45.448 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.448 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.448 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.449 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 1, locked = false
15:27:45.450 770   PackageConfigPersister  system_server                        W  App-specific configuration not found for packageName: com.czur.starry.device.settings and userId: 0
15:27:45.543 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:45.589 482   resolv                  netd                                 I  GetHostByNameHandler::run: {104 786536 104 983144 1000 0}
15:27:45.589 482   resolv                  netd                                 I  res_nmkquery: (QUERY, IN, A)
15:27:45.589 482   resolv                  netd                                 I  resolv_cache_lookup: FOUND IN CACHE entry=0xb400007c67e4c540
15:27:45.614 482   resolv                  netd                                 I  GetHostByAddrHandler::run: {104 786536 104 983144 1000 0}
15:27:45.614 482   resolv                  netd                                 I  res_nmkquery: (QUERY, IN, PTR)
15:27:45.614 482   resolv                  netd                                 I  resolv_cache_lookup: FOUND IN CACHE entry=0xb400007ca7e4be90
15:27:45.614 482   resolv                  netd                                 I  res_nquery: rcode = (NXDOMAIN), counts = an:0 ns:1 ar:0
15:27:45.628 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.637 17145 Chen                    com.czur.starry.device.launcher      D  startActivity
15:27:45.645 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:45.651 770   CellBroadcastUtils      system_server                        E  getDefaultCellBroadcastReceiverPackageName: no package found
15:27:45.666 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:45.667 770   Camera                  system_server                        I  callprocess:com.czur.starry.device.settings
15:27:45.669 981   KeyguardService         com.android.systemui                 D  setOccluded(false)
15:27:45.670 981   KeyguardViewMediator    com.android.systemui                 D  setOccluded(false)
15:27:45.671 981   WindowManagerShell      com.android.systemui                 D  onActivityRestartAttempt: ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity}
15:27:45.672 770   ActivityTaskManager     system_server                        I  START u0 {flg=0x18000000 cmp=com.czur.starry.device.settings/.ReNameDialogActivity (has extras)} with LAUNCH_SINGLE_INSTANCE from uid 1000 (BAL_ALLOW_ALLOWLISTED_UID) result code=3
15:27:45.702 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:45.715 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.716 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.717 981   KeyguardViewMediator    com.android.systemui                 D  handleSetOccluded(false)
15:27:45.717 981   KeyguardViewMediator    com.android.systemui                 D  KeyguardViewMediator queue processing message: SET_OCCLUDED
15:27:45.719 981   WindowManagerShell      com.android.systemui                 V  Transition animation finished (aborted=false), notifying core (#215)android.os.BinderProxy@a348a14@0
15:27:45.721 770   WindowManager           system_server                        V  Finish Transition #215: created at 06-07 15:27:45.234 collect-started=0.062ms request-sent=7.024ms started=11.184ms ready=116.576ms sent=172.248ms finished=486.746ms
15:27:45.723 981   WindowManagerShell      com.android.systemui                 V  Track 0 became idle
15:27:45.723 981   WindowManagerShell      com.android.systemui                 V  All active transition animations finished
15:27:45.741 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:45.742 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=456, alloc_height=596
15:27:45.742 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#14(BLAST Consumer)14'. share_fd : 9, share_attr_fd : 10, width : 456, height : 596, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1106176, layer_count : 1, backing_store_id: 0x1ef00000800
15:27:45.742 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 1856, alloc_width : 456, alloc_height : 596
15:27:45.742 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:45.744 2004  GoogleInpu...hodService com...gle.android.inputmethod.latin  I  GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.czur.starry.device.settings, inputType=1, inputTypeString=Normal[], enableLearning=true, autoCorrection=true, autoComplete=true, imeOptions=8000006, privateImeOptions=null, actionName=DONE, actionLabel=null, initialSelStart=0, initialSelEnd=0, initialCapsMode=3000, label=null, fieldId=**********, fieldName=null, extras=Bundle[mParcelledData.dataSize=240], hintText=non-empty, hintLocales=[]}}, true)
15:27:45.744 2004  SessionManager          com...gle.android.inputmethod.latin  W  SessionManager.beginSession():53 Try to begin an already begun session [INPUT_SESSION], end it first
15:27:45.745 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.745 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.745 2004  EmojiCompatManager      com...gle.android.inputmethod.latin  W  EmojiCompatManager.getEmojiCompatIfLoaded():336 EmojiCompat failed to load.
15:27:45.748 2004  Module                  com...gle.android.inputmethod.latin  I  DeviceLockedStatusModuleProvider$Module.updateDeviceLockedStatus():100 repeatCheckTimes = 1, locked = false
15:27:45.753 770   PackageConfigPersister  system_server                        W  App-specific configuration not found for packageName: com.czur.starry.device.settings and userId: 0
15:27:45.757 770   WindowManager           system_server                        V  getPackagePerformanceMode -- ComponentInfo{com.czur.starry.device.settings/com.czur.starry.device.settings.ReNameDialogActivity} -- com.czur.starry.device.settings -- mode=0
15:27:45.759 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.759 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.760 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.818 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:45.822 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.823 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.895 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.895 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.896 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.973 770   InputDispatcher         system_server                        D  Conflicting pointer actions: Down received while already down or hovering.
15:27:45.980 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:45.981 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:46.061 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:46.062 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:46.063 17558 VRI[ReName...gActivity] com.czur.starry.device.settings      D  updatePointerIcon called with position out of bounds
15:27:46.206 495   android.ha...V1-service and...graphics.allocator-V1-service  I  alloc_format: FMT:0x1,MOD:0
15:27:46.206 495   android.ha...V1-service and...graphics.allocator-V1-service  I  mali_gralloc_adjust_dimensions: alloc_format=FMT:0x1,MOD:0 usage=0x40000000000b00 alloc_width=456, alloc_height=596
15:27:46.207 495   android.ha...V1-service and...graphics.allocator-V1-service  D  got new private_handle_t instance for buffer 'VRI[ReNameDialogActivity]#14(BLAST Consumer)14'. share_fd : 9, share_attr_fd : 10, width : 456, height : 596, req_format : 0x1, producer_usage : 0x40000000000b00, consumer_usage : 0x40000000000b00, , stride : 0, alloc_format_base: 1, alloc_format_modifiers: 0x00000000, size : 1106176, layer_count : 1, backing_store_id: 0x1ef00000801
15:27:46.207 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[0]: offset : 0, byte_stride : 1856, alloc_width : 456, alloc_height : 596
15:27:46.207 495   android.ha...V1-service and...graphics.allocator-V1-service  D  plane_info[1]: offset : 0, byte_stride : 0, alloc_width : 0, alloc_height : 0
15:27:46.615 482   resolv                  netd                                 I  GetHostByAddrHandler::run: {104 786536 104 983144 1000 0}
15:27:46.615 482   resolv                  netd                                 I  res_nmkquery: (QUERY, IN, PTR)
15:27:46.615 482   resolv                  netd                                 I  resolv_cache_lookup: FOUND IN CACHE entry=0xb400007ca7e4be90
15:27:46.615 482   resolv                  netd                                 I  res_nquery: rcode = (NXDOMAIN), counts = an:0 ns:1 ar:0
15:27:47.227 2088  EShareServ...nfigParser com.ecloud.eshare.server             E  checkChengZhe, CBP02C2412000001, XWfHNna9E4i7Ng==
15:27:47.228 2088  EShareServ...workHelper com.ecloud.eshare.server             E  updateNetworkInternal, <ap>, **************, *************, CBP02C2412000001, XWfHNna9E4i7Ng==, 3, [**************, *************]
15:27:47.228 2088  EShareServ...workHelper com.ecloud.eshare.server             W  updateNetwork, sameNetwork, 1|**************|*************|CBP02C2412000001|XWfHNna9E4i7Ng==
15:27:47.228 2088  EShareServ...workHelper com.ecloud.eshare.server             E  updateNetwork, AUTO_REFRESH_NETWORK_INTERVAL.
15:27:47.616 482   resolv                  netd                                 I  GetHostByAddrHandler::run: {104 786536 104 983144 1000 0}
15:27:47.617 482   resolv                  netd                                 I  res_nmkquery: (QUERY, IN, PTR)
15:27:47.617 482   resolv                  netd                                 I  resolv_cache_lookup: FOUND IN CACHE entry=0xb400007ca7e4be90
15:27:47.617 482   resolv                  netd                                 I  res_nquery: rcode = (NXDOMAIN), counts = an:0 ns:1 ar:0
15:27:48.022 506   WifiHAL                 android.hardware.wifi-service        I  Creating message to get link statistics; iface = 10
15:27:48.031 506   WifiHAL                 android.hardware.wifi-service        I  In GetLinkStatsCommand::handleResponse
15:27:50.250 11673 <no-tag>                kworker/u16                          W  enter into es8156_set_bias_level, level = 2
15:27:50.253 11673 <no-tag>                kworker/u16                          W  enter into es8156_set_bias_level, level = 1
15:27:50.255 11673 <no-tag>                kworker/u16                          W  enter into es8156_set_bias_level, level = 0
